//
// Created by lz592 on 25-8-5.
//

#include "force_directed_layout.h"
#include <algorithm>
#include <cmath>
#include <iostream>
#include <limits>
#include <random>

#ifndef M_PI
#define M_PI 3.14159265358979323846
#endif

ForceDirectedLayout::ForceDirectedLayout()
    : current_temperature_(1.0) {
}

void ForceDirectedLayout::set_graph(const Graph& graph) {
    graph_ = graph;
}

void ForceDirectedLayout::set_components(const std::vector<Component>& components) {
    components_ = components;
    node_to_comp_index_.clear();

    for (size_t i = 0; i < components_.size(); ++i) {
        node_to_comp_index_[components_[i].id] = static_cast<int>(i);
    }
}

void ForceDirectedLayout::add_component(const Component& component) {
    node_to_comp_index_[component.id] = static_cast<int>(components_.size());
    components_.push_back(component);
}

void ForceDirectedLayout::set_board_polygon(const Polygon& board_polygon) {
    board_polygon_ = board_polygon;
}

void ForceDirectedLayout::add_keepout_polygon(const Polygon& keepout_polygon) {
    keepout_polygons_.push_back(keepout_polygon);
}

void ForceDirectedLayout::clear_keepout_polygons() {
    keepout_polygons_.clear();
}

void ForceDirectedLayout::set_parameters(const Parameters& params) {
    params_ = params;
}

ForceDirectedLayout::Parameters ForceDirectedLayout::get_parameters() const {
    return params_;
}

std::unordered_map<std::string, Point2D> ForceDirectedLayout::get_current_positions() const {
    std::unordered_map<std::string, Point2D> positions;
    for (const auto& comp : components_) {
        positions[comp.id] = comp.position;
    }
    return positions;
}

std::vector<double> ForceDirectedLayout::get_energy_history() const {
    return energy_history_;
}

Component* ForceDirectedLayout::find_component(const std::string& node_id) {
    auto it = node_to_comp_index_.find(node_id);
    if (it != node_to_comp_index_.end()) {
        return &components_[it->second];
    }
    return nullptr;
}

const Component* ForceDirectedLayout::find_component(const std::string& node_id) const {
    auto it = node_to_comp_index_.find(node_id);
    if (it != node_to_comp_index_.end()) {
        return &components_[it->second];
    }
    return nullptr;
}

std::unordered_map<std::string, Point2D> ForceDirectedLayout::optimize_layout() {
    initialize_algorithm();

    double prev_energy = std::numeric_limits<double>::infinity();

    for (int i = 0; i < params_.iterations; ++i) {
        calculate_forces();
        update_positions(0.1);

        if (i % 10 == 0) {
            double current_energy = calculate_total_energy();
            energy_history_.push_back(current_energy);

            if (check_convergence(current_energy, prev_energy)) {
                std::cout << "Converged at iteration " << i
                         << ", Energy: " << current_energy << std::endl;
                break;
            }

            prev_energy = current_energy;

            if (i % 100 == 0) {
                std::cout << "Iteration: " << i
                         << ", Energy: " << current_energy
                         << ", Temperature: " << current_temperature_ << std::endl;
            }
        }

        update_temperature(i);
    }

    std::cout << "Optimization complete, final energy: "
              << (energy_history_.empty() ? 0.0 : energy_history_.back()) << std::endl;

    return get_current_positions();
}

void ForceDirectedLayout::initialize_algorithm() {
    current_temperature_ = params_.temperature;
    energy_history_.clear();
    save_initial_relative_positions();

    // 重置所有组件的力和速度
    for (auto& comp : components_) {
        comp.reset_forces();
        comp.velocity = Point2D(0.0, 0.0);
    }
}

void ForceDirectedLayout::save_initial_relative_positions() {
    initial_relative_positions_.clear();

    // 计算组件中心
    Point2D component_center(0.0, 0.0);
    for (const auto& comp : components_) {
        component_center += comp.position;
    }
    component_center = component_center / static_cast<double>(components_.size());

    // 保存每个组件相对于中心的向量
    for (const auto& comp : components_) {
        initial_relative_positions_[comp.id] = comp.position - component_center;
    }
}

bool ForceDirectedLayout::check_convergence(double current_energy, double prev_energy) {
    return std::abs(current_energy - prev_energy) < params_.energy_threshold;
}

void ForceDirectedLayout::update_temperature(int iteration) {
    // 更缓慢的冷却策略
    if (iteration < params_.iterations * 0.1) {
        // 初期保持高温，充分探索
        current_temperature_ *= 0.999;
    } else if (iteration < params_.iterations * 0.5) {
        current_temperature_ *= 0.997;  // 中期缓慢降温
    } else {
        current_temperature_ *= params_.cooling_rate;  // 后期正常降温
    }
}

void ForceDirectedLayout::calculate_forces() {
    // 重置所有力
    for (auto& comp : components_) {
        comp.reset_forces();
    }

    calculate_repulsion_forces();
    calculate_attraction_forces();
    calculate_boundary_forces();
    calculate_keepout_forces();
    calculate_topology_preservation_forces();
}

void ForceDirectedLayout::calculate_repulsion_forces() {
    for (size_t i = 0; i < components_.size(); ++i) {
        for (size_t j = i + 1; j < components_.size(); ++j) {
            Component& comp1 = components_[i];
            Component& comp2 = components_[j];

            // 计算组件间距离
            Point2D vec = comp1.position - comp2.position;
            double distance = vec.norm();

            if (distance < 1e-5) {  // 避免除以零
                distance = 1e-5;
                vec = Point2D(1e-5, 1e-5);  // 随机方向，避免零向量
            }

            // 计算两个矩形实际的重叠情况
            double dx = std::abs(comp1.position.x - comp2.position.x);
            double dy = std::abs(comp1.position.y - comp2.position.y);

            // 水平和垂直方向上的重叠量
            double overlap_x = (comp1.width + comp2.width) / 2 - dx;
            double overlap_y = (comp1.height + comp2.height) / 2 - dy;

            Point2D force(0.0, 0.0);

            // 检测是否重叠
            if (overlap_x > 0 && overlap_y > 0) {  // 有重叠
                // 重叠面积越大，斥力越大
                double overlap_area = overlap_x * overlap_y;
                // 使用更强的非线性斥力模型
                double force_magnitude = params_.k_rep * (5.0 + overlap_area / (comp1.width * comp1.height));

                // 确保力的方向是将组件分开的方向
                if (vec.is_zero()) {
                    // 完全重叠的情况，给一个随机方向
                    static std::random_device rd;
                    static std::mt19937 gen(rd());
                    std::uniform_real_distribution<> dis(0.0, 2.0 * M_PI);
                    double angle = dis(gen);
                    vec = Point2D(std::cos(angle), std::sin(angle));
                } else {
                    vec = vec.normalized();  // 单位向量
                }
                force = force_magnitude * vec;
            } else {
                // 最小安全距离(组件尺寸之和的一半加上额外间距)
                double min_distance = (comp1.width + comp2.width) / 2 +
                                     (comp1.height + comp2.height) / 2 + 2.0;
                if (distance < min_distance) {
                    // 使用更平滑的距离函数
                    double force_magnitude = params_.k_rep * (1.0 / (distance * distance + 1e-5)) *
                                           ((min_distance / distance) - 0.5);
                    force = force_magnitude * (vec / distance);
                }
            }

            comp1.apply_force(force);
            comp2.apply_force(-force);
        }
    }
}

void ForceDirectedLayout::calculate_attraction_forces() {
    auto edges = graph_.get_edges();

    for (const auto& edge : edges) {
        Component* comp1 = find_component(edge.first);
        Component* comp2 = find_component(edge.second);

        if (!comp1 || !comp2) continue;

        Point2D vec = comp1->position - comp2->position;
        double distance = vec.norm();

        if (distance < 1e-5) {
            distance = 1e-5;
            vec = Point2D(1e-5, 0);
        }

        // 理想长度为两个组件对角线长度之和
        double ideal_length = std::sqrt(comp1->height * comp1->height / 4 + comp1->width * comp1->width / 4) +
                             std::sqrt(comp2->height * comp2->height / 4 + comp2->width * comp2->width / 4);

        // 引力 - 胡克定律(F = -k * (d - L0))
        double force_magnitude = -params_.k_att * (distance - ideal_length) * 1.0;  // weight=1.0
        Point2D force = force_magnitude * (vec / distance);

        comp1->apply_force(force);
        comp2->apply_force(-force);
    }
}

void ForceDirectedLayout::calculate_boundary_forces() {
    if (board_polygon_.empty()) return;

    for (auto& comp : components_) {
        auto corners = comp.get_corners();
        std::vector<bool> corners_inside;

        for (const auto& corner : corners) {
            corners_inside.push_back(Geometry::is_point_in_polygon(corner, board_polygon_));
        }

        bool any_inside = std::any_of(corners_inside.begin(), corners_inside.end(), [](bool b) { return b; });
        bool all_inside = std::all_of(corners_inside.begin(), corners_inside.end(), [](bool b) { return b; });

        // 如果组件完全在板框外
        if (!any_inside) {
            auto result = Geometry::distance_to_polygon(comp.position, board_polygon_);
            // 施加更强的力
            double force_magnitude = params_.k_rep * 5.0 * (1.0 + result.distance / 100);
            Point2D force = force_magnitude * (-result.direction);  // 方向向板框内部
            comp.apply_force(force);
        }
        // 如果组件部分在板框外
        else if (!all_inside) {
            for (size_t i = 0; i < corners.size(); ++i) {
                if (!corners_inside[i]) {
                    auto result = Geometry::distance_to_polygon(corners[i], board_polygon_);
                    double force_magnitude = params_.k_rep * 2.0 / (result.distance + 1e-5);
                    Point2D force = -force_magnitude * result.direction;
                    comp.apply_force(force);
                }
            }
        }
        // 组件完全在板框内，但接近边界
        else {
            auto result = Geometry::distance_to_polygon(comp.position, board_polygon_);
            double boundary_margin = comp.get_min_boundary_distance();
            if (result.distance < boundary_margin) {
                double force_magnitude = params_.k_rep * 0.1 * (1.0 - result.distance / boundary_margin);
                Point2D force = force_magnitude * result.direction;
                comp.apply_force(force);
            }
        }
    }
}

void ForceDirectedLayout::calculate_keepout_forces() {
    if (keepout_polygons_.empty()) return;

    for (auto& comp : components_) {
        Polygon comp_polygon = comp.get_bounding_polygon();

        for (const auto& keepout_polygon : keepout_polygons_) {
            if (Geometry::polygons_intersect(comp_polygon, keepout_polygon)) {
                auto result = Geometry::distance_to_polygon(comp.position, keepout_polygon);
                Point2D direction = result.direction;
                double force_magnitude;

                if (Geometry::is_point_in_polygon(comp.position, keepout_polygon)) {
                    direction = -direction;
                    force_magnitude = params_.k_rep * 5.0;
                } else {
                    force_magnitude = params_.k_rep * 2.0 / (result.distance + 1e-5);
                }

                Point2D force = force_magnitude * direction;
                comp.apply_force(force);
            } else {
                double min_dist = Geometry::min_distance_to_polygon(comp_polygon, keepout_polygon);
                double safety_margin = std::max(comp.width, comp.height) * 0.3;  // 安全距离

                if (min_dist < safety_margin) {
                    auto result = Geometry::distance_to_polygon(comp.position, keepout_polygon);
                    double force_magnitude = params_.k_rep * 0.5 * (1.0 - min_dist / safety_margin);
                    Point2D force = force_magnitude * result.direction;
                    comp.apply_force(force);
                }
            }
        }
    }
}

void ForceDirectedLayout::calculate_topology_preservation_forces() {
    if (initial_relative_positions_.empty()) return;

    // 计算当前布局中心点
    Point2D current_center(0.0, 0.0);
    for (const auto& comp : components_) {
        current_center += comp.position;
    }
    current_center = current_center / static_cast<double>(components_.size());

    for (auto& comp : components_) {
        if (!comp.is_fixed) {
            auto it = initial_relative_positions_.find(comp.id);
            if (it != initial_relative_positions_.end()) {
                // 期望位置 = 当前中心 + 初始相对向量
                Point2D expected_position = current_center + it->second;

                // 计算约束力
                Point2D position_diff = expected_position - comp.position;
                Point2D topology_force = position_diff * 5.0;

                comp.apply_force(topology_force);
            }
        }
    }
}

void ForceDirectedLayout::update_positions(double time_step) {
    for (auto& comp : components_) {
        if (comp.is_fixed) {
            continue;
        }

        // 增加温度对加速度的缩放影响
        Point2D acceleration = comp.net_force * std::min(1.0, current_temperature_);

        // 随机扰动，帮助跳出局部最优
        if (current_temperature_ > 0.3) {  // 只在温度较高时添加随机扰动
            static std::random_device rd;
            static std::mt19937 gen(rd());
            std::normal_distribution<> dis(0.0, current_temperature_ * 0.5);
            Point2D random_force(dis(gen), dis(gen));
            acceleration += random_force;
        }

        // 更新速度和位置
        comp.velocity = comp.velocity * params_.damping + acceleration * time_step;

        // 限制最大速度
        double velocity_mag = comp.velocity.norm();
        if (velocity_mag > params_.max_velocity) {
            comp.velocity = comp.velocity * (params_.max_velocity / velocity_mag);
        }

        comp.position += comp.velocity * time_step;
    }
}

double ForceDirectedLayout::calculate_total_energy() {
    double energy = 0.0;

    // 斥力能量
    for (size_t i = 0; i < components_.size(); ++i) {
        for (size_t j = i + 1; j < components_.size(); ++j) {
            const Component& comp1 = components_[i];
            const Component& comp2 = components_[j];
            double distance = comp1.distance_to(comp2);
            double min_distance = (comp1.width + comp2.width) / 2 + (comp1.height + comp2.height) / 2;

            if (distance < min_distance) {
                energy += params_.k_rep * (1.0 / (distance + 1e-5));
            }
        }
    }

    // 引力能量
    auto edges = graph_.get_edges();
    for (const auto& edge : edges) {
        const Component* comp1 = find_component(edge.first);
        const Component* comp2 = find_component(edge.second);

        if (comp1 && comp2) {
            double distance = comp1->distance_to(*comp2);
            double ideal_length = std::sqrt(comp1->height * comp1->height / 4 + comp1->width * comp1->width / 4) +
                                 std::sqrt(comp2->height * comp2->height / 4 + comp2->width * comp2->width / 4);
            energy += 0.5 * params_.k_att * (distance - ideal_length) * (distance - ideal_length) * 1.0;  // weight=1.0
        }
    }

    return energy;
}
