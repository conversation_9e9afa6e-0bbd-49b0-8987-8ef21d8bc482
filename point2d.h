#ifndef POINT2D_H
#define POINT2D_H

#include <cmath>

/**
 * 2D点类，替代Python中的numpy数组进行2D向量运算
 */
class Point2D {
public:
    double x, y;

    // 构造函数
    Point2D(double x = 0.0, double y = 0.0) : x(x), y(y) {}

    // 向量运算
    Point2D operator+(const Point2D& other) const {
        return Point2D(x + other.x, y + other.y);
    }

    Point2D operator-(const Point2D& other) const {
        return Point2D(x - other.x, y - other.y);
    }

    Point2D operator*(double scalar) const {
        return Point2D(x * scalar, y * scalar);
    }

    Point2D operator/(double scalar) const {
        return Point2D(x / scalar, y / scalar);
    }

    Point2D operator-() const {
        return Point2D(-x, -y);
    }

    Point2D& operator+=(const Point2D& other) {
        x += other.x;
        y += other.y;
        return *this;
    }

    Point2D& operator-=(const Point2D& other) {
        x -= other.x;
        y -= other.y;
        return *this;
    }

    Point2D& operator*=(double scalar) {
        x *= scalar;
        y *= scalar;
        return *this;
    }

    // 向量长度（模）
    double norm() const {
        return std::sqrt(x * x + y * y);
    }

    // 向量长度的平方（避免开方运算）
    double norm_squared() const {
        return x * x + y * y;
    }

    // 归一化向量
    Point2D normalized() const {
        double n = norm();
        if (n < 1e-10) {
            return Point2D(0.0, 0.0);
        }
        return Point2D(x / n, y / n);
    }

    // 点积
    double dot(const Point2D& other) const {
        return x * other.x + y * other.y;
    }

    // 叉积（2D中返回标量）
    double cross(const Point2D& other) const {
        return x * other.y - y * other.x;
    }

    // 距离计算
    double distance_to(const Point2D& other) const {
        return (*this - other).norm();
    }

    // 距离平方（避免开方运算）
    double distance_squared_to(const Point2D& other) const {
        return (*this - other).norm_squared();
    }

    // 零向量判断
    bool is_zero(double epsilon = 1e-10) const {
        return norm() < epsilon;
    }

    // 相等判断
    bool equals(const Point2D& other, double epsilon = 1e-10) const {
        return distance_to(other) < epsilon;
    }
};

// 标量乘法（标量在前）
inline Point2D operator*(double scalar, const Point2D& point) {
    return point * scalar;
}

#endif // POINT2D_H
