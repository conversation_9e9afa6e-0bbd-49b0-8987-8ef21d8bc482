#include <iostream>
#include "force_directed_layout.h"
#include "point2d.h"
#include "geometry.h"
#include "component.h"
#include "graph.h"

void test_basic_functionality() {
    std::cout << "=== Testing Basic Functionality ===" << std::endl;

    // 测试Point2D
    Point2D p1(1.0, 2.0);
    Point2D p2(4.0, 6.0);
    Point2D p3 = p1 + p2;
    std::cout << "Point addition: (" << p3.x << ", " << p3.y << ")" << std::endl;
    std::cout << "Distance: " << p1.distance_to(p2) << std::endl;

    // 测试Component
    Component comp1("C1", false, 10.0, 8.0, Point2D(0.0, 0.0));
    Component comp2("C2", false, 12.0, 6.0, Point2D(20.0, 15.0));
    std::cout << "Component distance: " << comp1.distance_to(comp2) << std::endl;

    // 测试Graph
    Graph graph;
    graph.add_node("C1");
    graph.add_node("C2");
    graph.add_node("C3");
    graph.add_edge("C1", "C2");
    graph.add_edge("C2", "C3");

    std::cout << "Graph nodes: " << graph.node_count() << std::endl;
    std::cout << "Graph edges: " << graph.edge_count() << std::endl;

    auto neighbors = graph.get_neighbors("C2");
    std::cout << "C2 neighbors: ";
    for (const auto& neighbor : neighbors) {
        std::cout << neighbor << " ";
    }
    std::cout << std::endl;
}

void test_simple_layout() {
    std::cout << "\n=== Testing Simple Layout ===" << std::endl;

    // 创建一个简单的3组件布局测试
    ForceDirectedLayout layout;

    // 设置图
    Graph graph;
    graph.add_node("C1");
    graph.add_node("C2");
    graph.add_node("C3");
    graph.add_edge("C1", "C2");
    graph.add_edge("C2", "C3");
    layout.set_graph(graph);

    // 创建组件
    std::vector<Component> components = {
        Component("C1", false, 10.0, 8.0, Point2D(0.0, 0.0)),
        Component("C2", false, 12.0, 6.0, Point2D(5.0, 5.0)),
        Component("C3", false, 8.0, 10.0, Point2D(10.0, 0.0))
    };
    layout.set_components(components);

    // 设置板框（一个简单的矩形）
    Polygon board_polygon = {
        Point2D(-50.0, -50.0),
        Point2D(50.0, -50.0),
        Point2D(50.0, 50.0),
        Point2D(-50.0, 50.0)
    };
    layout.set_board_polygon(board_polygon);

    // 设置参数
    ForceDirectedLayout::Parameters params;
    params.iterations = 100;  // 减少迭代次数用于测试
    params.k_rep = 1e5;
    params.k_att = 0.1;
    layout.set_parameters(params);

    // 执行布局优化
    std::cout << "Starting layout optimization..." << std::endl;
    auto final_positions = layout.optimize_layout();

    // 输出结果
    std::cout << "Final positions:" << std::endl;
    for (const auto& pos : final_positions) {
        std::cout << pos.first << ": (" << pos.second.x << ", " << pos.second.y << ")" << std::endl;
    }
}

int main() {
    std::cout << "Force Directed Layout C++ Implementation" << std::endl;
    std::cout << "========================================" << std::endl;

    try {
        test_basic_functionality();
        test_simple_layout();

        std::cout << "\nAll tests completed successfully!" << std::endl;
    } catch (const std::exception& e) {
        std::cerr << "Error: " << e.what() << std::endl;
        return 1;
    }

    return 0;
}