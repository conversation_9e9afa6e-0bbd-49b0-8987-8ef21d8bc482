#include <iostream>
#include <iomanip>
#include "modern_force_layout.h"

using namespace ForceLayout;

void test_modern_types() {
    std::cout << "=== Testing Modern Types ===" << std::endl;

    // 测试Point (std::complex<double>)
    Point p1 = make_point(1.0, 2.0);
    Point p2 = make_point(4.0, 6.0);
    Point p3 = p1 + p2;

    std::cout << "Point addition: (" << x(p3) << ", " << y(p3) << ")" << std::endl;
    std::cout << "Distance: " << distance(p1, p2) << std::endl;
    std::cout << "Cross product: " << cross(p1, p2) << std::endl;
    std::cout << "Dot product: " << dot(p1, p2) << std::endl;

    // 测试Component
    ComponentProperties props1{10.0, 8.0, false, 1.0};
    ComponentProperties props2{12.0, 6.0, false, 1.0};

    Component comp1("C1", props1, make_point(0.0, 0.0));
    Component comp2("C2", props2, make_point(20.0, 15.0));

    std::cout << "Component distance: " << comp1.distance_to(comp2) << std::endl;
    std::cout << "C1 bounding box size: " << comp1.bounding_box().size() << std::endl;

    // 测试Graph
    Graph graph;
    graph.add_node("C1");
    graph.add_node("C2");
    graph.add_node("C3");
    graph.add_edge("C1", "C2");
    graph.add_edge("C2", "C3");

    std::cout << "Graph nodes: " << graph.node_count() << std::endl;
    std::cout << "Graph edges: " << graph.edge_count() << std::endl;

    const auto& neighbors = graph.neighbors("C2");
    std::cout << "C2 neighbors: ";
    for (const auto& neighbor : neighbors) {
        std::cout << neighbor << " ";
    }
    std::cout << std::endl;
}

void test_geometry_functions() {
    std::cout << "\n=== Testing Geometry Functions ===" << std::endl;

    // 测试多边形
    Polygon triangle = {
        make_point(0.0, 0.0),
        make_point(10.0, 0.0),
        make_point(5.0, 8.0)
    };

    Point test_point = make_point(5.0, 3.0);
    bool inside = Geometry::point_in_polygon(test_point, triangle);
    std::cout << "Point (5,3) in triangle: " << (inside ? "yes" : "no") << std::endl;

    Point centroid = Geometry::polygon_centroid(triangle);
    std::cout << "Triangle centroid: (" << x(centroid) << ", " << y(centroid) << ")" << std::endl;

    auto dist_result = Geometry::point_to_polygon_distance(make_point(15.0, 5.0), triangle);
    if (dist_result) {
        std::cout << "Distance to triangle: " << dist_result->distance << std::endl;
    }
}

void test_modern_layout() {
    std::cout << "\n=== Testing Modern Layout Engine ===" << std::endl;

    // 创建布局引擎
    LayoutParameters params;
    params.max_iterations = 100;  // 减少迭代次数用于测试
    params.repulsion_strength = 1e5;
    params.attraction_strength = 0.1;

    ForceDirectedLayoutEngine engine(params);

    // 创建组件
    std::vector<Component> components;
    components.emplace_back("C1", ComponentProperties{10.0, 8.0, false, 1.0}, make_point(0.0, 0.0));
    components.emplace_back("C2", ComponentProperties{12.0, 6.0, false, 1.0}, make_point(5.0, 5.0));
    components.emplace_back("C3", ComponentProperties{8.0, 10.0, false, 1.0}, make_point(10.0, 0.0));

    engine.set_components(std::move(components));

    // 设置图
    Graph graph;
    graph.add_node("C1");
    graph.add_node("C2");
    graph.add_node("C3");
    graph.add_edge("C1", "C2");
    graph.add_edge("C2", "C3");

    engine.set_graph(std::move(graph));

    // 设置板框（一个简单的矩形）
    Polygon board_boundary = {
        make_point(-50.0, -50.0),
        make_point(50.0, -50.0),
        make_point(50.0, 50.0),
        make_point(-50.0, 50.0)
    };
    engine.set_board_boundary(std::move(board_boundary));

    // 执行布局优化
    std::cout << "Starting modern layout optimization..." << std::endl;
    auto final_positions = engine.optimize();

    // 输出结果
    std::cout << "Final positions:" << std::endl;
    std::cout << std::fixed << std::setprecision(2);
    for (const auto& pos : final_positions) {
        std::cout << pos.first << ": (" << x(pos.second) << ", " << y(pos.second) << ")" << std::endl;
    }

    // 输出能量历史
    const auto& energy_hist = engine.energy_history();
    if (!energy_hist.empty()) {
        std::cout << "Energy progression: " << energy_hist.front()
                  << " -> " << energy_hist.back() << std::endl;
    }
}

int main() {
    std::cout << "Modern Force Directed Layout C++14 Implementation" << std::endl;
    std::cout << "=================================================" << std::endl;

    try {
        test_modern_types();
        test_geometry_functions();
        test_modern_layout();

        std::cout << "\nAll tests completed successfully!" << std::endl;
        std::cout << "\nKey improvements in this modern design:" << std::endl;
        std::cout << "- Uses std::complex<double> for 2D points (natural vector operations)" << std::endl;
        std::cout << "- RAII and move semantics for better performance" << std::endl;
        std::cout << "- Template-based Result type for error handling" << std::endl;
        std::cout << "- Functional programming style with STL algorithms" << std::endl;
        std::cout << "- Cleaner separation of concerns" << std::endl;
        std::cout << "- Modern C++14 features without going beyond the standard" << std::endl;

    } catch (const std::exception& e) {
        std::cerr << "Error: " << e.what() << std::endl;
        return 1;
    }

    return 0;
}