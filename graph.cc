#include "graph.h"
#include <stdexcept>
#include <algorithm>

Graph::Graph() : next_index_(0) {
}

void Graph::add_node(const std::string& node) {
    if (has_node(node)) {
        return; // 节点已存在
    }
    
    node_to_index_[node] = next_index_;
    index_to_node_.push_back(node);
    adjacency_list_.emplace_back(); // 添加空的邻接集合
    next_index_++;
}

void Graph::add_edge(const std::string& node1, const std::string& node2) {
    // 确保两个节点都存在
    ensure_node_exists(node1);
    ensure_node_exists(node2);
    
    if (node1 == node2) {
        return; // 不允许自环
    }
    
    int index1 = node_to_index_[node1];
    int index2 = node_to_index_[node2];
    
    // 添加无向边
    adjacency_list_[index1].insert(index2);
    adjacency_list_[index2].insert(index1);
}

void Graph::remove_node(const std::string& node) {
    if (!has_node(node)) {
        return;
    }
    
    int node_index = node_to_index_[node];
    
    // 移除所有与该节点相关的边
    for (int neighbor_index : adjacency_list_[node_index]) {
        adjacency_list_[neighbor_index].erase(node_index);
    }
    
    // 清空该节点的邻接列表
    adjacency_list_[node_index].clear();
    
    // 从映射中移除
    node_to_index_.erase(node);
    
    // 注意：为了简化实现，我们不重新整理索引
    // 这意味着index_to_node_和adjacency_list_可能包含"空洞"
    // 在实际应用中，可以考虑更复杂的索引管理策略
}

void Graph::remove_edge(const std::string& node1, const std::string& node2) {
    if (!has_node(node1) || !has_node(node2)) {
        return;
    }
    
    int index1 = node_to_index_[node1];
    int index2 = node_to_index_[node2];
    
    adjacency_list_[index1].erase(index2);
    adjacency_list_[index2].erase(index1);
}

bool Graph::has_node(const std::string& node) const {
    return node_to_index_.find(node) != node_to_index_.end();
}

bool Graph::has_edge(const std::string& node1, const std::string& node2) const {
    if (!has_node(node1) || !has_node(node2)) {
        return false;
    }
    
    int index1 = node_to_index_.at(node1);
    int index2 = node_to_index_.at(node2);
    
    return adjacency_list_[index1].find(index2) != adjacency_list_[index1].end();
}

std::vector<std::string> Graph::get_neighbors(const std::string& node) const {
    if (!has_node(node)) {
        return {};
    }
    
    int node_index = node_to_index_.at(node);
    std::vector<std::string> neighbors;
    
    for (int neighbor_index : adjacency_list_[node_index]) {
        if (neighbor_index < index_to_node_.size()) {
            neighbors.push_back(index_to_node_[neighbor_index]);
        }
    }
    
    return neighbors;
}

int Graph::get_degree(const std::string& node) const {
    if (!has_node(node)) {
        return 0;
    }
    
    int node_index = node_to_index_.at(node);
    return static_cast<int>(adjacency_list_[node_index].size());
}

std::vector<std::string> Graph::get_nodes() const {
    std::vector<std::string> nodes;
    for (const auto& pair : node_to_index_) {
        nodes.push_back(pair.first);
    }
    return nodes;
}

std::vector<std::pair<std::string, std::string>> Graph::get_edges() const {
    std::vector<std::pair<std::string, std::string>> edges;
    
    for (const auto& pair : node_to_index_) {
        const std::string& node1 = pair.first;
        int index1 = pair.second;
        
        for (int index2 : adjacency_list_[index1]) {
            if (index2 < index_to_node_.size() && index1 < index2) {
                // 只添加一次边（避免重复）
                const std::string& node2 = index_to_node_[index2];
                edges.emplace_back(node1, node2);
            }
        }
    }
    
    return edges;
}

int Graph::node_count() const {
    return static_cast<int>(node_to_index_.size());
}

int Graph::edge_count() const {
    int count = 0;
    for (const auto& neighbors : adjacency_list_) {
        count += static_cast<int>(neighbors.size());
    }
    return count / 2; // 无向图，每条边被计算两次
}

void Graph::clear() {
    node_to_index_.clear();
    index_to_node_.clear();
    adjacency_list_.clear();
    next_index_ = 0;
}

bool Graph::empty() const {
    return node_to_index_.empty();
}

int Graph::get_node_index(const std::string& node) const {
    auto it = node_to_index_.find(node);
    if (it == node_to_index_.end()) {
        throw std::runtime_error("Node not found: " + node);
    }
    return it->second;
}

void Graph::ensure_node_exists(const std::string& node) {
    if (!has_node(node)) {
        add_node(node);
    }
}
