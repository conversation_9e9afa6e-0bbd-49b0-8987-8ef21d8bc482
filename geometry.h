#ifndef GEOMETRY_H
#define GEOMETRY_H

#include "point2d.h"
#include <vector>
#include <utility>

// 类型别名
using Polygon = std::vector<Point2D>;
using Segment = std::pair<Point2D, Point2D>;

/**
 * 几何计算函数集合，对应Python代码中的几何算法
 */
namespace Geometry {

    /**
     * 射线法判断点是否在多边形内部
     * 对应Python中的is_point_in_polygon函数
     */
    bool is_point_in_polygon(const Point2D& point, const Polygon& polygon);

    /**
     * 计算点到线段的最短距离和最近点
     * 对应Python中的distance_to_segment函数
     */
    std::pair<double, Point2D> distance_to_segment(const Point2D& point, const Segment& segment);

    /**
     * 计算点到多边形的最短距离、最近点和方向向量
     * 对应Python中的distance_to_polygon函数
     */
    struct DistanceResult {
        double distance;
        Point2D closest_point;
        Point2D direction;
    };
    DistanceResult distance_to_polygon(const Point2D& point, const Polygon& polygon);

    /**
     * 计算多边形的中心点（质心）
     * 对应Python中的calculate_polygon_centroid函数
     */
    Point2D calculate_polygon_centroid(const Polygon& polygon);

    /**
     * 判断两个多边形是否相交
     * 对应Python中的polygons_intersect函数
     */
    bool polygons_intersect(const Polygon& polygon1, const Polygon& polygon2);

    /**
     * 判断两条线段是否相交
     * 对应Python中的segments_intersect函数
     */
    bool segments_intersect(const Segment& segment1, const Segment& segment2);

    /**
     * 计算两个多边形之间的最小距离
     * 对应Python中的min_distance_to_polygon函数
     */
    double min_distance_to_polygon(const Polygon& polygon1, const Polygon& polygon2);

    // 辅助函数
    namespace Detail {
        /**
         * 计算三点的方向（叉积符号）
         */
        double direction(const Point2D& p, const Point2D& q, const Point2D& r);

        /**
         * 检查点是否在线段上
         */
        bool on_segment(const Point2D& p, const Point2D& q, const Point2D& r);
    }

} // namespace Geometry

#endif // GEOMETRY_H
