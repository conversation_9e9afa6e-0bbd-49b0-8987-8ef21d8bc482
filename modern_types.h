#ifndef MODERN_TYPES_H
#define MODERN_TYPES_H

#include <complex>
#include <vector>
#include <string>
#include <memory>
#include <functional>
#include <unordered_map>
#include <unordered_set>

namespace ForceLayout {

// 使用std::complex<double>作为2D点，自然支持向量运算
using Point = std::complex<double>;
using Polygon = std::vector<Point>;
using ComponentId = std::string;

// 便利函数
inline double x(const Point& p) { return p.real(); }
inline double y(const Point& p) { return p.imag(); }
inline Point make_point(double x, double y) { return Point(x, y); }

// 向量运算辅助函数
inline double norm_squared(const Point& p) { return std::norm(p); }
inline double distance(const Point& a, const Point& b) { return std::abs(a - b); }
inline double distance_squared(const Point& a, const Point& b) { return norm_squared(a - b); }

// 叉积（2D中返回标量）
inline double cross(const Point& a, const Point& b) {
    return x(a) * y(b) - y(a) * x(b);
}

// 点积
inline double dot(const Point& a, const Point& b) {
    return x(a) * x(b) + y(a) * y(b);
}

// 归一化向量
inline Point normalize(const Point& p) {
    double mag = std::abs(p);
    return mag > 1e-10 ? p / mag : Point(0, 0);
}

// 组件属性结构体，使用聚合初始化
struct ComponentProperties {
    double width;
    double height;
    bool is_fixed = false;
    double thermal_rating = 1.0;
};

// 物理状态结构体
struct PhysicsState {
    Point position{0, 0};
    Point velocity{0, 0};
    Point force{0, 0};
    
    void reset_force() { force = Point(0, 0); }
    void apply_force(const Point& f) { force += f; }
};

// 组件类 - 更现代的设计
class Component {
private:
    ComponentId id_;
    ComponentProperties props_;
    PhysicsState physics_;

public:
    Component(ComponentId id, ComponentProperties props, Point initial_pos = Point(0, 0))
        : id_(std::move(id)), props_(props) {
        physics_.position = initial_pos;
    }

    // 访问器
    const ComponentId& id() const { return id_; }
    const ComponentProperties& properties() const { return props_; }
    const PhysicsState& physics() const { return physics_; }
    PhysicsState& physics() { return physics_; }
    
    // 便利方法
    Point position() const { return physics_.position; }
    void set_position(const Point& pos) { physics_.position = pos; }
    
    double width() const { return props_.width; }
    double height() const { return props_.height; }
    bool is_fixed() const { return props_.is_fixed; }
    
    // 几何方法
    Polygon bounding_box() const {
        double hw = props_.width / 2.0;
        double hh = props_.height / 2.0;
        Point center = physics_.position;
        
        return {
            center + Point(-hw, -hh),  // 左下
            center + Point(hw, -hh),   // 右下
            center + Point(hw, hh),    // 右上
            center + Point(-hw, hh)    // 左上
        };
    }
    
    double distance_to(const Component& other) const {
        return distance(physics_.position, other.physics_.position);
    }
};

// 图的现代实现 - 使用邻接表
class Graph {
private:
    std::unordered_map<ComponentId, std::unordered_set<ComponentId>> adjacency_;
    
public:
    void add_node(const ComponentId& node) {
        adjacency_[node]; // 创建空的邻接集合
    }
    
    void add_edge(const ComponentId& a, const ComponentId& b) {
        adjacency_[a].insert(b);
        adjacency_[b].insert(a);
    }
    
    bool has_node(const ComponentId& node) const {
        return adjacency_.find(node) != adjacency_.end();
    }
    
    bool has_edge(const ComponentId& a, const ComponentId& b) const {
        auto it = adjacency_.find(a);
        return it != adjacency_.end() && it->second.count(b) > 0;
    }
    
    const std::unordered_set<ComponentId>& neighbors(const ComponentId& node) const {
        static const std::unordered_set<ComponentId> empty;
        auto it = adjacency_.find(node);
        return it != adjacency_.end() ? it->second : empty;
    }
    
    std::vector<ComponentId> nodes() const {
        std::vector<ComponentId> result;
        result.reserve(adjacency_.size());
        for (const auto& pair : adjacency_) {
            result.push_back(pair.first);
        }
        return result;
    }
    
    std::vector<std::pair<ComponentId, ComponentId>> edges() const {
        std::vector<std::pair<ComponentId, ComponentId>> result;
        for (const auto& pair : adjacency_) {
            for (const auto& neighbor : pair.second) {
                if (pair.first < neighbor) { // 避免重复边
                    result.emplace_back(pair.first, neighbor);
                }
            }
        }
        return result;
    }
    
    size_t node_count() const { return adjacency_.size(); }
    
    size_t edge_count() const {
        size_t count = 0;
        for (const auto& pair : adjacency_) {
            count += pair.second.size();
        }
        return count / 2; // 无向图
    }
};

} // namespace ForceLayout

#endif // MODERN_TYPES_H
