#ifndef MODERN_GEOMETRY_H
#define MODERN_GEOMETRY_H

#include "modern_types.h"
#include <algorithm>
#include <limits>
#include <utility>

namespace ForceLayout {
namespace Geometry {

// 结果类型，避免使用异常
template<typename T>
struct Result {
    T value;
    bool valid;
    
    Result() : valid(false) {}
    Result(T val) : value(std::move(val)), valid(true) {}
    
    explicit operator bool() const { return valid; }
    const T& operator*() const { return value; }
    T& operator*() { return value; }
};

// 距离查询结果
struct DistanceResult {
    double distance;
    Point closest_point;
    Point direction;
};

// 射线法判断点是否在多边形内
inline bool point_in_polygon(const Point& point, const Polygon& polygon) {
    if (polygon.size() < 3) return false;
    
    double px = x(point), py = y(point);
    bool inside = false;
    
    for (size_t i = 0, j = polygon.size() - 1; i < polygon.size(); j = i++) {
        double xi = x(polygon[i]), yi = y(polygon[i]);
        double xj = x(polygon[j]), yj = y(polygon[j]);
        
        if (((yi > py) != (yj > py)) && 
            (px < (xj - xi) * (py - yi) / (yj - yi) + xi)) {
            inside = !inside;
        }
    }
    return inside;
}

// 点到线段的距离
inline std::pair<double, Point> point_to_segment_distance(
    const Point& point, const Point& seg_start, const Point& seg_end) {
    
    Point seg_vec = seg_end - seg_start;
    double seg_len_sq = norm_squared(seg_vec);
    
    if (seg_len_sq < 1e-10) {
        // 线段退化为点
        return {distance(point, seg_start), seg_start};
    }
    
    // 投影参数
    double t = dot(point - seg_start, seg_vec) / seg_len_sq;
    t = std::max(0.0, std::min(1.0, t)); // 限制在[0,1]
    
    Point closest = seg_start + t * seg_vec;
    return {distance(point, closest), closest};
}

// 点到多边形的距离
inline Result<DistanceResult> point_to_polygon_distance(
    const Point& point, const Polygon& polygon) {
    
    if (polygon.empty()) {
        return Result<DistanceResult>();
    }
    
    double min_dist = std::numeric_limits<double>::infinity();
    Point closest_point;
    
    for (size_t i = 0; i < polygon.size(); ++i) {
        size_t next = (i + 1) % polygon.size();
        auto [dist, closest] = point_to_segment_distance(point, polygon[i], polygon[next]);
        
        if (dist < min_dist) {
            min_dist = dist;
            closest_point = closest;
        }
    }
    
    Point direction = normalize(point - closest_point);
    return DistanceResult{min_dist, closest_point, direction};
}

// 计算多边形质心
inline Point polygon_centroid(const Polygon& polygon) {
    if (polygon.size() < 3) {
        // 退化情况，返回几何中心
        Point sum(0, 0);
        for (const auto& p : polygon) {
            sum += p;
        }
        return sum / static_cast<double>(polygon.size());
    }
    
    double area = 0.0;
    Point centroid(0, 0);
    
    for (size_t i = 0; i < polygon.size(); ++i) {
        size_t next = (i + 1) % polygon.size();
        double cross_prod = cross(polygon[i], polygon[next]);
        area += cross_prod;
        centroid += (polygon[i] + polygon[next]) * cross_prod;
    }
    
    area *= 0.5;
    if (std::abs(area) < 1e-10) {
        // 面积为0，返回几何中心
        Point sum(0, 0);
        for (const auto& p : polygon) {
            sum += p;
        }
        return sum / static_cast<double>(polygon.size());
    }
    
    return centroid / (6.0 * area);
}

// 线段相交检测
inline bool segments_intersect(const Point& p1, const Point& p2, 
                              const Point& p3, const Point& p4) {
    auto orientation = [](const Point& p, const Point& q, const Point& r) {
        return cross(q - p, r - p);
    };
    
    auto on_segment = [](const Point& p, const Point& q, const Point& r) {
        return std::min(x(p), x(r)) <= x(q) && x(q) <= std::max(x(p), x(r)) &&
               std::min(y(p), y(r)) <= y(q) && y(q) <= std::max(y(p), y(r));
    };
    
    double o1 = orientation(p1, p2, p3);
    double o2 = orientation(p1, p2, p4);
    double o3 = orientation(p3, p4, p1);
    double o4 = orientation(p3, p4, p2);
    
    // 一般情况
    if ((o1 > 0) != (o2 > 0) && (o3 > 0) != (o4 > 0)) {
        return true;
    }
    
    // 特殊情况：共线
    if (std::abs(o1) < 1e-10 && on_segment(p1, p3, p2)) return true;
    if (std::abs(o2) < 1e-10 && on_segment(p1, p4, p2)) return true;
    if (std::abs(o3) < 1e-10 && on_segment(p3, p1, p4)) return true;
    if (std::abs(o4) < 1e-10 && on_segment(p3, p2, p4)) return true;
    
    return false;
}

// 多边形相交检测
inline bool polygons_intersect(const Polygon& poly1, const Polygon& poly2) {
    // 检查顶点是否在对方多边形内
    for (const auto& point : poly1) {
        if (point_in_polygon(point, poly2)) return true;
    }
    for (const auto& point : poly2) {
        if (point_in_polygon(point, poly1)) return true;
    }
    
    // 检查边相交
    for (size_t i = 0; i < poly1.size(); ++i) {
        size_t next1 = (i + 1) % poly1.size();
        for (size_t j = 0; j < poly2.size(); ++j) {
            size_t next2 = (j + 1) % poly2.size();
            if (segments_intersect(poly1[i], poly1[next1], poly2[j], poly2[next2])) {
                return true;
            }
        }
    }
    
    return false;
}

// 两个多边形的最小距离
inline double polygon_to_polygon_distance(const Polygon& poly1, const Polygon& poly2) {
    if (polygons_intersect(poly1, poly2)) {
        return 0.0;
    }
    
    double min_dist = std::numeric_limits<double>::infinity();
    
    // 计算poly1的每个点到poly2的距离
    for (const auto& point : poly1) {
        auto result = point_to_polygon_distance(point, poly2);
        if (result && result->distance < min_dist) {
            min_dist = result->distance;
        }
    }
    
    // 计算poly2的每个点到poly1的距离
    for (const auto& point : poly2) {
        auto result = point_to_polygon_distance(point, poly1);
        if (result && result->distance < min_dist) {
            min_dist = result->distance;
        }
    }
    
    return min_dist;
}

} // namespace Geometry
} // namespace ForceLayout

#endif // MODERN_GEOMETRY_H
