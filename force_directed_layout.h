#ifndef FORCE_DIRECTED_LAYOUT_H
#define FORCE_DIRECTED_LAYOUT_H

#include "point2d.h"
#include "geometry.h"
#include "component.h"
#include "graph.h"
#include <unordered_map>
#include <vector>
#include <string>

/**
 * 力导向布局算法类
 * 对应Python中的force_directed_layout函数和相关功能
 */
class ForceDirectedLayout {
public:
    // 算法参数结构
    struct Parameters {
        int iterations = 500;
        double k_rep = 5e6;           // 斥力系数
        double k_att = 0.5;           // 引力系数
        double damping = 0.85;        // 阻尼系数
        double max_velocity = 10.0;   // 最大速度限制
        double temperature = 1.0;     // 初始温度（模拟退火）
        double cooling_rate = 0.99;   // 冷却速率
        double energy_threshold = 1e-3; // 收敛阈值
    };

private:
    Graph graph_;
    std::vector<Component> components_;
    std::unordered_map<std::string, int> node_to_comp_index_;

    Polygon board_polygon_;
    std::vector<Polygon> keepout_polygons_;

    Parameters params_;

    // 算法状态
    double current_temperature_;
    std::vector<double> energy_history_;
    std::unordered_map<std::string, Point2D> initial_relative_positions_;

public:
    // 构造函数
    ForceDirectedLayout();

    // 设置图和组件
    void set_graph(const Graph& graph);
    void set_components(const std::vector<Component>& components);
    void add_component(const Component& component);

    // 设置约束
    void set_board_polygon(const Polygon& board_polygon);
    void add_keepout_polygon(const Polygon& keepout_polygon);
    void clear_keepout_polygons();

    // 设置算法参数
    void set_parameters(const Parameters& params);
    Parameters get_parameters() const;

    // 执行布局优化
    std::unordered_map<std::string, Point2D> optimize_layout();

    // 获取当前组件位置
    std::unordered_map<std::string, Point2D> get_current_positions() const;

    // 获取能量历史
    std::vector<double> get_energy_history() const;

private:
    // 核心算法函数
    void calculate_forces();
    void update_positions(double time_step = 0.1);
    double calculate_total_energy();

    // 力计算函数
    void calculate_repulsion_forces();
    void calculate_attraction_forces();
    void calculate_boundary_forces();
    void calculate_keepout_forces();
    void calculate_topology_preservation_forces();

    // 辅助函数
    void initialize_algorithm();
    void save_initial_relative_positions();
    bool check_convergence(double current_energy, double prev_energy);
    void update_temperature(int iteration);

    // 组件查找
    Component* find_component(const std::string& node_id);
    const Component* find_component(const std::string& node_id) const;
};

#endif //FORCE_DIRECTED_LAYOUT_H