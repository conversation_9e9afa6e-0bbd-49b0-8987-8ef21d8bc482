#ifndef COMPONENT_H
#define COMPONENT_H

#include "point2d.h"
#include "geometry.h"
#include <string>

/**
 * 组件类，对应Python中的ComponentWrapper类
 * 包装EasyEDA组件为类似force.py中PCBComponent的接口
 */
class Component {
public:
    std::string id;           // 组件ID
    bool is_fixed;           // 是否固定位置
    double width;            // 组件宽度
    double height;           // 组件高度
    Point2D position;        // 当前位置
    Point2D net_force;       // 合力
    Point2D velocity;        // 速度
    double thermal_rating;   // 热评级

    // 构造函数
    Component(const std::string& name, bool fixed, double w, double h, 
              const Point2D& pos, double thermal = 1.0);

    // 默认构造函数
    Component() = default;

    // 计算到另一个组件的距离
    double distance_to(const Component& other_component) const;

    // 获取组件的边界框多边形（矩形的四个角点）
    Polygon get_bounding_polygon() const;

    // 获取组件的四个角点
    std::vector<Point2D> get_corners() const;

    // 检查组件是否与多边形相交
    bool intersects_with_polygon(const Polygon& polygon) const;

    // 检查组件是否完全在多边形内
    bool is_completely_inside_polygon(const Polygon& polygon) const;

    // 检查组件是否部分在多边形外
    bool is_partially_outside_polygon(const Polygon& polygon) const;

    // 重置力和速度
    void reset_forces();

    // 应用力
    void apply_force(const Point2D& force);

    // 更新位置（基于速度和时间步长）
    void update_position(double time_step, double damping = 0.85, double max_velocity = 10.0);

    // 获取组件中心到边界的最小距离（用于边界检查）
    double get_min_boundary_distance() const;
};

#endif // COMPONENT_H
