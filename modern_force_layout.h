#ifndef MODERN_FORCE_LAYOUT_H
#define MODERN_FORCE_LAYOUT_H

#include "modern_types.h"
#include "modern_geometry.h"
#include <algorithm>
#include <functional>
#include <random>

namespace ForceLayout {

// 算法参数配置
struct LayoutParameters {
    int max_iterations = 500;
    double repulsion_strength = 5e6;
    double attraction_strength = 0.5;
    double damping = 0.85;
    double max_velocity = 10.0;
    double initial_temperature = 1.0;
    double cooling_rate = 0.99;
    double convergence_threshold = 1e-3;
    double boundary_force_strength = 5.0;
    double keepout_force_strength = 2.0;
    double topology_preservation_strength = 5.0;
};

// 力计算函数类型
using ForceCalculator = std::function<void(std::vector<Component>&, const Graph&, const LayoutParameters&)>;

// 现代化的力导向布局引擎
class ForceDirectedLayoutEngine {
private:
    std::vector<Component> components_;
    Graph graph_;
    Polygon board_boundary_;
    std::vector<Polygon> keepout_regions_;
    LayoutParameters params_;
    
    // 算法状态
    double current_temperature_;
    std::vector<double> energy_history_;
    std::unordered_map<ComponentId, Point> initial_relative_positions_;
    
    // 随机数生成器
    mutable std::mt19937 rng_;
    
public:
    explicit ForceDirectedLayoutEngine(LayoutParameters params = LayoutParameters{})
        : params_(std::move(params)), current_temperature_(params_.initial_temperature), 
          rng_(std::random_device{}()) {}
    
    // 设置方法 - 使用移动语义
    void set_components(std::vector<Component> components) {
        components_ = std::move(components);
    }
    
    void set_graph(Graph graph) {
        graph_ = std::move(graph);
    }
    
    void set_board_boundary(Polygon boundary) {
        board_boundary_ = std::move(boundary);
    }
    
    void add_keepout_region(Polygon region) {
        keepout_regions_.emplace_back(std::move(region));
    }
    
    void clear_keepout_regions() {
        keepout_regions_.clear();
    }
    
    // 执行布局优化
    std::unordered_map<ComponentId, Point> optimize() {
        initialize_optimization();
        
        double prev_energy = std::numeric_limits<double>::infinity();
        
        for (int iteration = 0; iteration < params_.max_iterations; ++iteration) {
            // 计算所有力
            calculate_all_forces();
            
            // 更新位置
            update_positions();
            
            // 检查收敛
            if (iteration % 10 == 0) {
                double current_energy = calculate_total_energy();
                energy_history_.push_back(current_energy);
                
                if (has_converged(current_energy, prev_energy)) {
                    break;
                }
                prev_energy = current_energy;
            }
            
            // 更新温度
            update_temperature(iteration);
        }
        
        return extract_positions();
    }
    
    // 获取能量历史
    const std::vector<double>& energy_history() const {
        return energy_history_;
    }
    
private:
    void initialize_optimization() {
        current_temperature_ = params_.initial_temperature;
        energy_history_.clear();
        save_initial_topology();
        
        // 重置物理状态
        for (auto& comp : components_) {
            comp.physics().reset_force();
            comp.physics().velocity = Point(0, 0);
        }
    }
    
    void save_initial_topology() {
        initial_relative_positions_.clear();
        
        // 计算质心
        Point centroid(0, 0);
        for (const auto& comp : components_) {
            centroid += comp.position();
        }
        centroid /= static_cast<double>(components_.size());
        
        // 保存相对位置
        for (const auto& comp : components_) {
            initial_relative_positions_[comp.id()] = comp.position() - centroid;
        }
    }
    
    void calculate_all_forces() {
        // 重置所有力
        for (auto& comp : components_) {
            comp.physics().reset_force();
        }
        
        calculate_repulsion_forces();
        calculate_attraction_forces();
        calculate_boundary_forces();
        calculate_keepout_forces();
        calculate_topology_preservation_forces();
    }
    
    void calculate_repulsion_forces() {
        for (size_t i = 0; i < components_.size(); ++i) {
            for (size_t j = i + 1; j < components_.size(); ++j) {
                auto& comp1 = components_[i];
                auto& comp2 = components_[j];
                
                Point vec = comp1.position() - comp2.position();
                double dist = std::abs(vec);
                
                if (dist < 1e-5) {
                    // 避免除零，给随机方向
                    std::uniform_real_distribution<> angle_dist(0, 2 * M_PI);
                    double angle = angle_dist(rng_);
                    vec = Point(std::cos(angle), std::sin(angle));
                    dist = 1e-5;
                }
                
                // 检查重叠
                double dx = std::abs(x(vec));
                double dy = std::abs(y(vec));
                double overlap_x = (comp1.width() + comp2.width()) / 2 - dx;
                double overlap_y = (comp1.height() + comp2.height()) / 2 - dy;
                
                if (overlap_x > 0 && overlap_y > 0) {
                    // 重叠情况
                    double overlap_area = overlap_x * overlap_y;
                    double force_mag = params_.repulsion_strength * 
                                     (5.0 + overlap_area / (comp1.width() * comp1.height()));
                    Point force = force_mag * normalize(vec);
                    
                    comp1.physics().apply_force(force);
                    comp2.physics().apply_force(-force);
                } else {
                    // 正常斥力
                    double min_dist = (comp1.width() + comp2.width() + 
                                     comp1.height() + comp2.height()) / 4 + 2.0;
                    if (dist < min_dist) {
                        double force_mag = params_.repulsion_strength / (dist * dist + 1e-5) *
                                         ((min_dist / dist) - 0.5);
                        Point force = force_mag * normalize(vec);
                        
                        comp1.physics().apply_force(force);
                        comp2.physics().apply_force(-force);
                    }
                }
            }
        }
    }
    
    void calculate_attraction_forces() {
        for (const auto& edge : graph_.edges()) {
            auto* comp1 = find_component(edge.first);
            auto* comp2 = find_component(edge.second);
            
            if (!comp1 || !comp2) continue;
            
            Point vec = comp1->position() - comp2->position();
            double dist = std::abs(vec);
            
            if (dist < 1e-5) {
                dist = 1e-5;
                vec = Point(1e-5, 0);
            }
            
            // 理想距离
            double ideal_dist = std::sqrt(comp1->width() * comp1->width() + comp1->height() * comp1->height()) / 2 +
                               std::sqrt(comp2->width() * comp2->width() + comp2->height() * comp2->height()) / 2;
            
            // 胡克定律
            double force_mag = -params_.attraction_strength * (dist - ideal_dist);
            Point force = force_mag * normalize(vec);
            
            comp1->physics().apply_force(force);
            comp2->physics().apply_force(-force);
        }
    }
    
    void calculate_boundary_forces() {
        if (board_boundary_.empty()) return;
        
        for (auto& comp : components_) {
            auto bbox = comp.bounding_box();
            
            // 检查是否完全在边界内
            bool all_inside = std::all_of(bbox.begin(), bbox.end(), 
                [this](const Point& p) { return Geometry::point_in_polygon(p, board_boundary_); });
            
            if (!all_inside) {
                auto result = Geometry::point_to_polygon_distance(comp.position(), board_boundary_);
                if (result) {
                    double force_mag = params_.boundary_force_strength * (1.0 + result->distance / 100);
                    Point force = force_mag * (-result->direction);
                    comp.physics().apply_force(force);
                }
            }
        }
    }
    
    void calculate_keepout_forces() {
        for (auto& comp : components_) {
            auto bbox = comp.bounding_box();
            
            for (const auto& keepout : keepout_regions_) {
                if (Geometry::polygons_intersect(bbox, keepout)) {
                    auto result = Geometry::point_to_polygon_distance(comp.position(), keepout);
                    if (result) {
                        double force_mag = params_.keepout_force_strength / (result->distance + 1e-5);
                        Point force = force_mag * result->direction;
                        comp.physics().apply_force(force);
                    }
                }
            }
        }
    }
    
    void calculate_topology_preservation_forces() {
        if (initial_relative_positions_.empty()) return;
        
        // 计算当前质心
        Point current_centroid(0, 0);
        for (const auto& comp : components_) {
            current_centroid += comp.position();
        }
        current_centroid /= static_cast<double>(components_.size());
        
        for (auto& comp : components_) {
            if (comp.is_fixed()) continue;
            
            auto it = initial_relative_positions_.find(comp.id());
            if (it != initial_relative_positions_.end()) {
                Point expected_pos = current_centroid + it->second;
                Point force = params_.topology_preservation_strength * (expected_pos - comp.position());
                comp.physics().apply_force(force);
            }
        }
    }
    
    void update_positions() {
        const double time_step = 0.1;
        
        for (auto& comp : components_) {
            if (comp.is_fixed()) continue;
            
            auto& physics = comp.physics();
            
            // 温度影响的加速度
            Point acceleration = physics.force * std::min(1.0, current_temperature_);
            
            // 随机扰动
            if (current_temperature_ > 0.3) {
                std::normal_distribution<> noise_dist(0.0, current_temperature_ * 0.5);
                Point random_force(noise_dist(rng_), noise_dist(rng_));
                acceleration += random_force;
            }
            
            // 更新速度
            physics.velocity = physics.velocity * params_.damping + acceleration * time_step;
            
            // 限制速度
            double vel_mag = std::abs(physics.velocity);
            if (vel_mag > params_.max_velocity) {
                physics.velocity *= params_.max_velocity / vel_mag;
            }
            
            // 更新位置
            physics.position += physics.velocity * time_step;
        }
    }
    
    double calculate_total_energy() {
        double energy = 0.0;
        
        // 斥力能量
        for (size_t i = 0; i < components_.size(); ++i) {
            for (size_t j = i + 1; j < components_.size(); ++j) {
                double dist = components_[i].distance_to(components_[j]);
                double min_dist = (components_[i].width() + components_[j].width() + 
                                 components_[i].height() + components_[j].height()) / 4;
                if (dist < min_dist) {
                    energy += params_.repulsion_strength / (dist + 1e-5);
                }
            }
        }
        
        // 引力能量
        for (const auto& edge : graph_.edges()) {
            auto* comp1 = find_component(edge.first);
            auto* comp2 = find_component(edge.second);
            if (comp1 && comp2) {
                double dist = comp1->distance_to(*comp2);
                double ideal_dist = (comp1->width() + comp1->height() + 
                                   comp2->width() + comp2->height()) / 4;
                energy += 0.5 * params_.attraction_strength * 
                         (dist - ideal_dist) * (dist - ideal_dist);
            }
        }
        
        return energy;
    }
    
    bool has_converged(double current_energy, double prev_energy) const {
        return std::abs(current_energy - prev_energy) < params_.convergence_threshold;
    }
    
    void update_temperature(int iteration) {
        if (iteration < params_.max_iterations * 0.1) {
            current_temperature_ *= 0.999;
        } else if (iteration < params_.max_iterations * 0.5) {
            current_temperature_ *= 0.997;
        } else {
            current_temperature_ *= params_.cooling_rate;
        }
    }
    
    Component* find_component(const ComponentId& id) {
        auto it = std::find_if(components_.begin(), components_.end(),
            [&id](const Component& comp) { return comp.id() == id; });
        return it != components_.end() ? &(*it) : nullptr;
    }
    
    std::unordered_map<ComponentId, Point> extract_positions() const {
        std::unordered_map<ComponentId, Point> positions;
        for (const auto& comp : components_) {
            positions[comp.id()] = comp.position();
        }
        return positions;
    }
};

} // namespace ForceLayout

#endif // MODERN_FORCE_LAYOUT_H
