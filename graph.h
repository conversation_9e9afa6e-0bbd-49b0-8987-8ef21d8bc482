#ifndef GRAPH_H
#define GRAPH_H

#include <string>
#include <vector>
#include <unordered_map>
#include <unordered_set>

/**
 * 图数据结构类，替代Python中的networkx.Graph
 * 使用邻接表实现无向图
 */
class Graph {
private:
    // 节点名称到索引的映射
    std::unordered_map<std::string, int> node_to_index_;
    
    // 索引到节点名称的映射
    std::vector<std::string> index_to_node_;
    
    // 邻接表，存储每个节点的邻居索引
    std::vector<std::unordered_set<int>> adjacency_list_;
    
    // 下一个可用的节点索引
    int next_index_;

public:
    // 构造函数
    Graph();

    // 添加节点
    void add_node(const std::string& node);

    // 添加边（无向图）
    void add_edge(const std::string& node1, const std::string& node2);

    // 移除节点
    void remove_node(const std::string& node);

    // 移除边
    void remove_edge(const std::string& node1, const std::string& node2);

    // 检查节点是否存在
    bool has_node(const std::string& node) const;

    // 检查边是否存在
    bool has_edge(const std::string& node1, const std::string& node2) const;

    // 获取节点的邻居
    std::vector<std::string> get_neighbors(const std::string& node) const;

    // 获取节点的度数
    int get_degree(const std::string& node) const;

    // 获取所有节点
    std::vector<std::string> get_nodes() const;

    // 获取所有边
    std::vector<std::pair<std::string, std::string>> get_edges() const;

    // 获取节点数量
    int node_count() const;

    // 获取边数量
    int edge_count() const;

    // 清空图
    void clear();

    // 检查图是否为空
    bool empty() const;

    // 获取节点的索引（内部使用）
    int get_node_index(const std::string& node) const;

private:
    // 确保节点存在，如果不存在则添加
    void ensure_node_exists(const std::string& node);
};

#endif // GRAPH_H
