#include "geometry.h"

#include <algorithm>
#include <cmath>
#include <limits>
#include <stdexcept>

namespace Geometry {

bool is_point_in_polygon(const Point2D& point, const Polygon& polygon) {
    if (polygon.size() < 3) return false;
    
    double x = point.x, y = point.y;
    int n = polygon.size();
    bool inside = false;

    double p1x = polygon[0].x, p1y = polygon[0].y;
    for (int i = 1; i <= n; i++) {
        double p2x = polygon[i % n].x, p2y = polygon[i % n].y;
        if (y > std::min(p1y, p2y)) {
            if (y <= std::max(p1y, p2y)) {
                if (x <= std::max(p1x, p2x)) {
                    if (p1y != p2y) {
                        double x_intersect = (y - p1y) * (p2x - p1x) / (p2y - p1y) + p1x;
                        if (p1x == p2x || x <= x_intersect) {
                            inside = !inside;
                        }
                    }
                }
            }
        }
        p1x = p2x;
        p1y = p2y;
    }
    return inside;
}

std::pair<double, Point2D> distance_to_segment(const Point2D& point, const Segment& segment) {
    const Point2D& p = segment.first;
    const Point2D& q = segment.second;
    
    // 向量PQ
    Point2D pq = q - p;
    
    // 如果线段是一个点
    if (pq.is_zero()) {
        double dist = point.distance_to(p);
        return {dist, p};
    }
    
    // 点在线段上的投影参数t
    Point2D point_to_p = point - p;
    double t = point_to_p.dot(pq) / pq.norm_squared();
    
    Point2D closest_point;
    if (t < 0) {
        // 最近点是P
        closest_point = p;
    } else if (t > 1) {
        // 最近点是Q
        closest_point = q;
    } else {
        // 最近点在线段PQ上
        closest_point = p + t * pq;
    }
    
    double dist = point.distance_to(closest_point);
    return {dist, closest_point};
}

DistanceResult distance_to_polygon(const Point2D& point, const Polygon& polygon) {
    if (polygon.empty()) {
        throw std::invalid_argument("多边形不能为空");
    }
    
    double min_dist = std::numeric_limits<double>::infinity();
    Point2D closest_point;
    
    int n = polygon.size();
    for (int i = 0; i < n; i++) {
        Segment segment = {polygon[i], polygon[(i + 1) % n]};
        auto [dist, point_on_segment] = distance_to_segment(point, segment);
        
        if (dist < min_dist) {
            min_dist = dist;
            closest_point = point_on_segment;
        }
    }
    
    // 计算方向向量 (从最近点指向原始点)
    Point2D direction = point - closest_point;
    
    // 归一化方向向量
    if (!direction.is_zero()) {
        direction = direction.normalized();
    }
    
    return {min_dist, closest_point, direction};
}

Point2D calculate_polygon_centroid(const Polygon& polygon) {
    int n = polygon.size();
    if (n < 3) {
        // 如果不是有效多边形，返回几何中心
        double cx = 0.0, cy = 0.0;
        for (const auto& p : polygon) {
            cx += p.x;
            cy += p.y;
        }
        return Point2D(cx / n, cy / n);
    }
    
    double area = 0.0;
    double cx = 0.0;
    double cy = 0.0;
    
    for (int i = 0; i < n; i++) {
        int j = (i + 1) % n;
        double cross = polygon[i].x * polygon[j].y - polygon[j].x * polygon[i].y;
        area += cross;
        cx += (polygon[i].x + polygon[j].x) * cross;
        cy += (polygon[i].y + polygon[j].y) * cross;
    }
    
    area *= 0.5;
    if (std::abs(area) < 1e-10) {
        // 如果面积为0，返回几何中心
        double gcx = 0.0, gcy = 0.0;
        for (const auto& p : polygon) {
            gcx += p.x;
            gcy += p.y;
        }
        return Point2D(gcx / n, gcy / n);
    } else {
        cx /= 6.0 * area;
        cy /= 6.0 * area;
    }
    
    return Point2D(cx, cy);
}

bool polygons_intersect(const Polygon& polygon1, const Polygon& polygon2) {
    // 检查一个多边形的点是否在另一个多边形内
    for (const auto& point : polygon1) {
        if (is_point_in_polygon(point, polygon2)) {
            return true;
        }
    }
    
    for (const auto& point : polygon2) {
        if (is_point_in_polygon(point, polygon1)) {
            return true;
        }
    }
    
    // 检查边是否相交
    int n1 = polygon1.size(), n2 = polygon2.size();
    for (int i = 0; i < n1; i++) {
        Segment edge1 = {polygon1[i], polygon1[(i + 1) % n1]};
        for (int j = 0; j < n2; j++) {
            Segment edge2 = {polygon2[j], polygon2[(j + 1) % n2]};
            if (segments_intersect(edge1, edge2)) {
                return true;
            }
        }
    }
    
    return false;
}

bool segments_intersect(const Segment& segment1, const Segment& segment2) {
    const Point2D& p1 = segment1.first;
    const Point2D& p2 = segment1.second;
    const Point2D& p3 = segment2.first;
    const Point2D& p4 = segment2.second;
    
    double d1 = Detail::direction(p3, p4, p1);
    double d2 = Detail::direction(p3, p4, p2);
    double d3 = Detail::direction(p1, p2, p3);
    double d4 = Detail::direction(p1, p2, p4);
    
    // 如果线段互相穿过
    if (((d1 > 0 && d2 < 0) || (d1 < 0 && d2 > 0)) && 
        ((d3 > 0 && d4 < 0) || (d3 < 0 && d4 > 0))) {
        return true;
    }
    
    // 处理共线情况
    if (std::abs(d1) < 1e-10 && Detail::on_segment(p3, p1, p4)) return true;
    if (std::abs(d2) < 1e-10 && Detail::on_segment(p3, p2, p4)) return true;
    if (std::abs(d3) < 1e-10 && Detail::on_segment(p1, p3, p2)) return true;
    if (std::abs(d4) < 1e-10 && Detail::on_segment(p1, p4, p2)) return true;
    
    return false;
}

double min_distance_to_polygon(const Polygon& polygon1, const Polygon& polygon2) {
    // 如果多边形相交，距离为0
    if (polygons_intersect(polygon1, polygon2)) {
        return 0.0;
    }
    
    double min_dist = std::numeric_limits<double>::infinity();
    
    // 计算polygon1的每个点到polygon2的最小距离
    for (const auto& point : polygon1) {
        auto result = distance_to_polygon(point, polygon2);
        min_dist = std::min(min_dist, result.distance);
    }
    
    // 计算polygon2的每个点到polygon1的最小距离
    for (const auto& point : polygon2) {
        auto result = distance_to_polygon(point, polygon1);
        min_dist = std::min(min_dist, result.distance);
    }
    
    return min_dist;
}

namespace Detail {
    double direction(const Point2D& p, const Point2D& q, const Point2D& r) {
        return (r - p).cross(q - p);
    }
    
    bool on_segment(const Point2D& p, const Point2D& q, const Point2D& r) {
        return (std::max(p.x, r.x) >= q.x && q.x >= std::min(p.x, r.x) &&
                std::max(p.y, r.y) >= q.y && q.y >= std::min(p.y, r.y));
    }
}

} // namespace Geometry
