#include "component.h"
#include <algorithm>
#include <cmath>

Component::Component(const std::string& name, bool fixed, double w, double h, 
                     const Point2D& pos, double thermal)
    : id(name), is_fixed(fixed), width(w), height(h), position(pos), 
      thermal_rating(thermal), net_force(0.0, 0.0), velocity(0.0, 0.0) {
}

double Component::distance_to(const Component& other_component) const {
    return position.distance_to(other_component.position);
}

Polygon Component::get_bounding_polygon() const {
    double half_width = width / 2.0;
    double half_height = height / 2.0;
    
    return {
        Point2D(position.x - half_width, position.y - half_height),  // 左下
        Point2D(position.x + half_width, position.y - half_height),  // 右下
        Point2D(position.x + half_width, position.y + half_height),  // 右上
        Point2D(position.x - half_width, position.y + half_height)   // 左上
    };
}

std::vector<Point2D> Component::get_corners() const {
    double half_width = width / 2.0;
    double half_height = height / 2.0;
    
    return {
        Point2D(position.x - half_width, position.y - half_height),  // 左下
        Point2D(position.x + half_width, position.y - half_height),  // 右下
        Point2D(position.x + half_width, position.y + half_height),  // 右上
        Point2D(position.x - half_width, position.y + half_height)   // 左上
    };
}

bool Component::intersects_with_polygon(const Polygon& polygon) const {
    Polygon comp_polygon = get_bounding_polygon();
    return Geometry::polygons_intersect(comp_polygon, polygon);
}

bool Component::is_completely_inside_polygon(const Polygon& polygon) const {
    auto corners = get_corners();
    for (const auto& corner : corners) {
        if (!Geometry::is_point_in_polygon(corner, polygon)) {
            return false;
        }
    }
    return true;
}

bool Component::is_partially_outside_polygon(const Polygon& polygon) const {
    auto corners = get_corners();
    bool any_inside = false;
    bool any_outside = false;
    
    for (const auto& corner : corners) {
        if (Geometry::is_point_in_polygon(corner, polygon)) {
            any_inside = true;
        } else {
            any_outside = true;
        }
    }
    
    return any_inside && any_outside;
}

void Component::reset_forces() {
    net_force = Point2D(0.0, 0.0);
}

void Component::apply_force(const Point2D& force) {
    net_force += force;
}

void Component::update_position(double time_step, double damping, double max_velocity) {
    if (is_fixed) {
        return;
    }
    
    // 更新速度：v = v * damping + a * dt
    velocity = velocity * damping + net_force * time_step;
    
    // 限制最大速度
    double velocity_magnitude = velocity.norm();
    if (velocity_magnitude > max_velocity) {
        velocity = velocity * (max_velocity / velocity_magnitude);
    }
    
    // 更新位置：pos = pos + v * dt
    position += velocity * time_step;
}

double Component::get_min_boundary_distance() const {
    return std::min(width, height) * 0.5;
}
